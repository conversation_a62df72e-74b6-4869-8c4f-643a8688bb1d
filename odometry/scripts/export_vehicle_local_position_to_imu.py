#!/usr/bin/env python3
"""
Export VehicleLocalPosition to IMU CSV Script

This script processes ROS2 bag files containing PX4 VehicleLocalPosition messages
and converts them to IMU-like data format, specifically extracting heading information
and converting it to quaternion orientation.

Key Features:
- Extracts VehicleLocalPosition messages from ROS2 bag files
- Converts heading field to quaternion representation (yaw-only rotation)
- Outputs IMU-compatible CSV format with proper timestamp handling
- Sets linear acceleration and angular velocity to zero as specified
- Uses rosbags library for consistent ROS2 bag processing

Data Conversion Details:
- Heading (yaw) → Quaternion: (x=0, y=0, z=sin(heading/2), w=cos(heading/2))
- Linear acceleration: Set to (0, 0, 0)
- Angular velocity: Set to (0, 0, 0)
- Only orientation z-component (yaw) contains valid data

Usage Examples:
    # Basic usage with VehicleLocalPosition topic
    python export_vehicle_local_position_to_imu.py \
        -i /path/to/bag/file \
        -n /fmu/out/vehicle_local_position \
        -o imu_data.csv

    # With custom start offset and ROS timestamp format
    python export_vehicle_local_position_to_imu.py \
        -i /media/aabouee/data/bags/Auterion/22.01.25.14-07-IR-9min-140 \
        -n /fmu/out/vehicle_local_position \
        -o ./output/imu_data.csv \
        --start_offset 100 \
        --time_format ros

Output CSV Format:
    timestamp,orientation_x,orientation_y,orientation_z,orientation_w,
    linear_acceleration_x,linear_acceleration_y,linear_acceleration_z,
    angular_velocity_x,angular_velocity_y,angular_velocity_z

Dependencies:
    - rosbags (for ROS2 bag processing)
    - Standard Python libraries: argparse, csv, logging, math, os, struct

Author: Generated for spl-gnss-denied-evaluation project
Compatible with: PX4 VehicleLocalPosition messages, ROS2 bag files
"""

import argparse
import csv
import logging.config
import math
import os
import struct

logging.config.fileConfig("../logging.conf")
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(
        description="Extract VehicleLocalPosition topics from a ROS2 bag file and convert to IMU CSV format"
    )
    parser.add_argument(
        "-n",
        "--topic_names",
        nargs="+",
        default=[],
        help="VehicleLocalPosition topic names to extract",
    )
    parser.add_argument(
        "-i", "--input_bag_file", default="", help="Path of input ROS2 bag file"
    )
    parser.add_argument(
        "-o", "--output_file", default="./imu_data.csv", help="Output CSV file path"
    )
    parser.add_argument(
        "--start_offset",
        type=int,
        default=0,
        help="Number of messages to skip from start",
    )
    parser.add_argument(
        "--time_format",
        default="ros",
        choices=["ros", "unix"],
        help="Timestamp format: 'ros' for ROS timestamp or 'unix' for Unix timestamp",
    )

    return parser.parse_args()


def parse_px4_vehicle_local_position(rawdata):
    """
    Parse PX4 VehicleLocalPosition message from raw CDR data.
    Based on the message definition from px4_msgs/msg/VehicleLocalPosition.msg
    """
    # CDR format: skip 4-byte header
    offset = 4

    # Parse fields according to VehicleLocalPosition.msg structure:
    # uint64 timestamp
    timestamp = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint64 timestamp_sample
    timestamp_sample = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # bool fields (xy_valid, z_valid, v_xy_valid, v_z_valid)
    xy_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    z_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    v_xy_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    v_z_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1

    # CDR alignment: 4 bytes padding to align float32 values on 4-byte boundary
    offset += 0  # Already aligned

    # Position fields (float32 x, y, z)
    x = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    y = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    z = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Skip position reset delta fields (float32[2] delta_xy, uint8 xy_reset_counter, etc.)
    # float32[2] delta_xy
    offset += 8  # 2 * 4 bytes
    # uint8 xy_reset_counter
    offset += 1
    # float32 delta_z
    offset += 4
    # uint8 z_reset_counter
    offset += 1

    # CDR alignment: 2 bytes padding
    offset += 2

    # Velocity fields (float32 vx, vy, vz, z_deriv)
    vx = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    vy = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    vz = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    z_deriv = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Skip velocity reset delta fields
    # float32[2] delta_vxy
    offset += 8  # 2 * 4 bytes
    # uint8 vxy_reset_counter
    offset += 1
    # float32 delta_vz
    offset += 4
    # uint8 vz_reset_counter
    offset += 1

    # CDR alignment: 2 bytes padding
    offset += 2

    # Acceleration fields (float32 ax, ay, az)
    ax = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    ay = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    az = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Heading field (float32 heading) - this is what we need for IMU orientation
    heading = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    return {
        "timestamp": timestamp,
        "timestamp_sample": timestamp_sample,
        "xy_valid": xy_valid,
        "z_valid": z_valid,
        "v_xy_valid": v_xy_valid,
        "v_z_valid": v_z_valid,
        "x": x,
        "y": y,
        "z": z,
        "vx": vx,
        "vy": vy,
        "vz": vz,
        "z_deriv": z_deriv,
        "ax": ax,
        "ay": ay,
        "az": az,
        "heading": heading,
    }


def heading_to_quaternion(heading):
    """
    Convert heading (yaw) to quaternion representation.
    Only the z-component (yaw) contains valid data.
    """
    # Convert heading to quaternion: (x=0, y=0, z=sin(heading/2), w=cos(heading/2))
    half_heading = heading / 2.0
    return [0.0, 0.0, math.sin(half_heading), math.cos(half_heading)]


def extract_vehicle_local_position_data(args):
    """Extract VehicleLocalPosition data from a ROS2 bag file and convert to IMU format."""
    from rosbags.rosbag2 import Reader

    imu_data = []
    skip_count = 0

    with Reader(args.input_bag_file) as reader:
        for connection, timestamp, rawdata in reader.messages():
            if connection.topic in args.topic_names:
                # Skip messages based on start_offset
                if skip_count < args.start_offset:
                    skip_count += 1
                    continue

                # Handle PX4 VehicleLocalPosition message type
                if connection.msgtype == "px4_msgs/msg/VehicleLocalPosition":
                    try:
                        # Parse the raw CDR data manually
                        vlp_data = parse_px4_vehicle_local_position(rawdata)

                        # Extract timestamp
                        if args.time_format == "ros":
                            # Convert microseconds to seconds with nanosecond precision format
                            timestamp_sec = vlp_data["timestamp"] // 1000000
                            timestamp_nsec = (vlp_data["timestamp"] % 1000000) * 1000
                            timestamp_val = f"{timestamp_sec}.{timestamp_nsec:09d}"
                        else:  # unix
                            # Convert microseconds to seconds
                            timestamp_val = vlp_data["timestamp"] * 1e-6

                        # Convert heading to quaternion (x, y, z, w)
                        quat = heading_to_quaternion(vlp_data["heading"])

                        # Create IMU-like data row: timestamp, qx, qy, qz, qw, ax, ay, az, gx, gy, gz
                        # Note: Setting linear acceleration and angular velocity to zero as requested
                        imu_data.append(
                            [
                                timestamp_val,
                                quat[0],  # orientation.x (always 0)
                                quat[1],  # orientation.y (always 0)
                                quat[2],  # orientation.z (sin(heading/2))
                                quat[3],  # orientation.w (cos(heading/2))
                                0.0,  # linear_acceleration.x (set to zero)
                                0.0,  # linear_acceleration.y (set to zero)
                                0.0,  # linear_acceleration.z (set to zero)
                                0.0,  # angular_velocity.x (set to zero)
                                0.0,  # angular_velocity.y (set to zero)
                                0.0,  # angular_velocity.z (set to zero)
                            ]
                        )

                        if len(imu_data) % 100 == 0:
                            logger.info(
                                f"Processed {len(imu_data)} VehicleLocalPosition messages"
                            )

                    except Exception as e:
                        logger.warning(
                            f"Failed to parse VehicleLocalPosition message: {e}"
                        )
                        continue

                else:
                    logger.warning(f"Unsupported message type: {connection.msgtype}")
                    continue

    return imu_data


def save_to_csv(imu_data, output_file):
    """Save IMU data to CSV file."""
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        # Write header
        writer.writerow(
            [
                "timestamp",
                "orientation_x",
                "orientation_y",
                "orientation_z",
                "orientation_w",
                "linear_acceleration_x",
                "linear_acceleration_y",
                "linear_acceleration_z",
                "angular_velocity_x",
                "angular_velocity_y",
                "angular_velocity_z",
            ]
        )
        # Write data
        writer.writerows(imu_data)

    logger.info(f"IMU data saved to {output_file} with {len(imu_data)} records")


def main():
    args = parser()

    if not args.topic_names:
        logger.error("Please specify at least one topic name using -n argument")
        return

    if not os.path.exists(args.input_bag_file):
        logger.error(f"Input bag file does not exist: {args.input_bag_file}")
        return

    logger.info(f"Processing topics: {args.topic_names}")
    logger.info(f"Input bag file: {args.input_bag_file}")
    logger.info(f"Output file: {args.output_file}")

    # Extract VehicleLocalPosition data and convert to IMU format
    imu_data = extract_vehicle_local_position_data(args)

    if imu_data:
        # Save to CSV file
        save_to_csv(imu_data, args.output_file)
        logger.info(
            f"Successfully processed {len(imu_data)} VehicleLocalPosition messages"
        )
    else:
        logger.warning("No VehicleLocalPosition data found in the specified topics")


if __name__ == "__main__":
    main()
