import argparse
import logging
import math
import os

from rosbags.rosbag2 import Reader, Writer
from rosbags.typesys import Stores, get_typestore
from rosbags.typesys.stores.ros2_foxy import (
    geometry_msgs__msg__Quaternion as Quaternion,
)
from rosbags.typesys.stores.ros2_foxy import sensor_msgs__msg__Imu as Imu
from rosbags.typesys.stores.ros2_foxy import std_msgs__msg__Header as Header

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(module)s:%(lineno)s]: %(message)s",
)
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(
        description="Extract image topic from a ros2 bag file, convert VehicleLocalPosition to IMU messages, and save both to a new bag file"
    )
    parser.add_argument(
        "-i", "--input_bag_file", default="", help="Path of input ros2 bag file"
    )
    parser.add_argument(
        "-o",
        "--output_folder",
        default="./output",
        help="Output folder path for the new bag",
    )
    parser.add_argument("--image_topic", default="", help="Image topic name to extract")
    parser.add_argument(
        "--vehicle_local_position_topic",
        default="",
        help="VehicleLocalPosition topic name to convert",
    )
    parser.add_argument(
        "--start_offset",
        type=int,
        default=0,
        help="Number of messages to skip from start",
    )

    return parser.parse_args()


def convert_vehicle_local_position_to_imu(msg, timestamp):
    """
    Convert VehicleLocalPosition message to IMU message.
    Only the heading is used to create a quaternion with x=0, y=0, z=heading, w=0.
    """
    # Create IMU message
    imu_msg = Imu()

    # Set header
    imu_msg.header = Header()
    imu_msg.header.stamp = msg.timestamp
    imu_msg.header.frame_id = "vehicle"

    # Convert heading to quaternion (x=0, y=0, z=heading, w=0)
    # But we need to create a proper quaternion from yaw angle
    yaw = msg.heading
    # For a proper quaternion from yaw only:
    # x = 0, y = 0, z = sin(yaw/2), w = cos(yaw/2)
    imu_msg.orientation = Quaternion()
    imu_msg.orientation.x = 0.0
    imu_msg.orientation.y = 0.0
    imu_msg.orientation.z = math.sin(yaw / 2.0)
    imu_msg.orientation.w = math.cos(yaw / 2.0)

    # Set covariance to indicate only z orientation is valid
    # Set first element to -1 to indicate other orientations are not estimated
    # For a proper covariance matrix with only yaw known:
    imu_msg.orientation_covariance = [
        -1,
        0,
        0,  # x orientation unknown
        0,
        -1,
        0,  # y orientation unknown
        0,
        0,
        1.0,  # z orientation known with variance 1.0 (can be adjusted)
    ]

    # Initialize other fields to zero
    imu_msg.angular_velocity.x = 0.0
    imu_msg.angular_velocity.y = 0.0
    imu_msg.angular_velocity.z = 0.0
    imu_msg.angular_velocity_covariance = [-1, 0, 0, 0, -1, 0, 0, 0, -1]

    imu_msg.linear_acceleration.x = 0.0
    imu_msg.linear_acceleration.y = 0.0
    imu_msg.linear_acceleration.z = 0.0
    imu_msg.linear_acceleration_covariance = [-1, 0, 0, 0, -1, 0, 0, 0, -1]

    return imu_msg


def extract_and_convert(args):
    """Extract image data and convert VehicleLocalPosition to IMU messages."""
    skip_count = 0
    typestore = get_typestore(Stores.ROS2_HUMBLE)

    # Create output bag
    output_bag_path = os.path.join(args.output_folder, "converted_data")
    os.makedirs(args.output_folder, exist_ok=True)

    with Reader(args.input_bag_file) as reader:
        with Writer(output_bag_path, version=8) as writer:
            # Create connections for output topics
            image_connection = None
            imu_connection = None

            # First pass to set up connections
            for connection in reader.connections:
                if connection.topic == args.image_topic:
                    # Use a default serialization format if not available
                    serialization_format = getattr(
                        connection, "serialization_format", "cdr"
                    )
                    # Use default QoS profiles if not available
                    qos_profiles = getattr(connection, "offered_qos_profiles", "")
                    image_connection = writer.add_connection(
                        args.image_topic,
                        connection.msgtype,
                        serialization_format,
                        qos_profiles,
                    )
                elif connection.topic == args.vehicle_local_position_topic:
                    # Add IMU connection
                    # Use default QoS profiles if not available
                    qos_profiles = getattr(connection, "offered_qos_profiles", "")
                    imu_connection = writer.add_connection(
                        "/imu",
                        "sensor_msgs/msg/Imu",
                        "cdr",
                        qos_profiles,
                    )

            # Check if we found the required topics
            if image_connection is None:
                logger.warning(f"Image topic '{args.image_topic}' not found in bag")
            if imu_connection is None:
                logger.warning(
                    f"VehicleLocalPosition topic '{args.vehicle_local_position_topic}' not found in bag"
                )

            # Second pass to process messages
            for connection, timestamp, rawdata in reader.messages():
                # Skip messages based on start_offset
                if skip_count < args.start_offset:
                    skip_count += 1
                    continue

                # Process image messages
                if connection.topic == args.image_topic:
                    # Write image data as-is to output bag
                    writer.write(image_connection, timestamp, rawdata)

                # Process VehicleLocalPosition messages
                elif connection.topic == args.vehicle_local_position_topic:
                    if connection.msgtype == "px4_msgs/msg/VehicleLocalPosition":
                        try:
                            # Deserialize the message
                            msg = typestore.deserialize_cdr(rawdata, connection.msgtype)

                            # Convert to IMU message
                            imu_msg = convert_vehicle_local_position_to_imu(
                                msg, timestamp
                            )

                            # Serialize and write IMU message
                            imu_data = typestore.serialize_cdr(
                                imu_msg, "sensor_msgs/msg/Imu"
                            )
                            writer.write(imu_connection, timestamp, imu_data)

                        except Exception as e:
                            logger.warning(
                                f"Failed to process VehicleLocalPosition message: {e}"
                            )
                            continue
                    else:
                        logger.warning(
                            f"Unsupported VehicleLocalPosition message type: {connection.msgtype}"
                        )
                        continue


def main():
    args = parser()

    if not args.image_topic:
        logger.error("Please specify an image topic name using --image_topic argument")
        return

    if not args.vehicle_local_position_topic:
        logger.error(
            "Please specify a VehicleLocalPosition topic name using --vehicle_local_position_topic argument"
        )
        return

    if not os.path.exists(args.input_bag_file):
        logger.error(f"Input bag file does not exist: {args.input_bag_file}")
        return

    logger.info(f"Extracting image data from topic: {args.image_topic}")
    logger.info(
        f"Converting VehicleLocalPosition from topic: {args.vehicle_local_position_topic}"
    )
    logger.info(f"Input bag file: {args.input_bag_file}")
    logger.info(f"Output folder: {args.output_folder}")

    # Extract and convert data
    try:
        extract_and_convert(args)
        logger.info("Successfully processed bag file")
    except Exception as e:
        logger.error(f"Error processing bag file: {e}")


if __name__ == "__main__":
    main()
