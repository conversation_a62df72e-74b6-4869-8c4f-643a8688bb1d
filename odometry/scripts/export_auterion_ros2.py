#!/usr/bin/env python3
"""
Comprehensive ROS2 Bag Processing Tool: Auterion PX4 Data Converter

This script processes ROS2 bag files containing PX4 sensor data and converts them to standard
ROS2 message formats while preserving image data unchanged.

Key Features:
- Processes image, VehicleLocalPosition, and SensorGps topics from ROS2 bag files
- Converts PX4 VehicleLocalPosition messages to standard sensor_msgs/Imu messages
- Converts PX4 SensorGps messages to standard sensor_msgs/NavSatFix messages
- Passes through image data completely unchanged (no modifications)
- Outputs a new ROS2 bag file with processed data
- Uses rosbags library for consistent ROS2 bag processing
- Preserves original timestamps for all message types

Data Conversion Details:
- VehicleLocalPosition heading → IMU quaternion: (x=0, y=0, z=sin(heading/2), w=cos(heading/2))
- Linear acceleration: Set to (0, 0, 0) with invalid covariance (-1)
- Angular velocity: Set to (0, 0, 0) with invalid covariance (-1)
- Only orientation z-component (yaw) contains valid data
- SensorGps → NavSatFix: Direct coordinate conversion with proper status fields

Supported Message Types:
- Input: px4_msgs/msg/VehicleLocalPosition → Output: sensor_msgs/msg/Imu
- Input: px4_msgs/msg/SensorGps → Output: sensor_msgs/msg/NavSatFix
- Input: sensor_msgs/msg/Image → Output: sensor_msgs/msg/Image (unchanged)
- Input: sensor_msgs/msg/CompressedImage → Output: sensor_msgs/msg/CompressedImage (unchanged)

Usage Examples:
    # Process image, IMU, and GPS topics
    python export_auterion_ros2.py \
        -i /path/to/input/bag \
        -n /camera/image_raw /fmu/out/vehicle_local_position /fmu/out/vehicle_gps_position \
        -o /path/to/output/bag

    # With custom start offset and ROS timestamp format
    python export_auterion_ros2.py \
        -i /media/aabouee/data/bags/Auterion/22.01.25.14-07-IR-9min-140 \
        -n /camera/image_raw /fmu/out/vehicle_local_position /fmu/out/vehicle_gps_position \
        -o ./output/processed_bag \
        --start_offset 100 \
        --time_format ros

Output ROS2 Bag Structure:
    - Original image topics: /camera/image_raw (sensor_msgs/msg/Image)
    - Converted IMU topic: /imu/data (sensor_msgs/msg/Imu)
    - Converted GPS topic: /gps/fix (sensor_msgs/msg/NavSatFix)

Dependencies:
    - rosbags (for ROS2 bag processing)
    - Standard Python libraries: argparse, logging, math, os, struct

Author: Generated for spl-gnss-denied-evaluation project
Compatible with: PX4 VehicleLocalPosition, SensorGps messages, ROS2 bag files, image topics
"""

import argparse
import logging.config
import math
import os
import struct

import numpy as np
from rosbags.rosbag2 import Reader, Writer
from rosbags.typesys import Stores, get_typestore
from rosbags.typesys.stores.ros2_humble import builtin_interfaces__msg__Time as Time
from rosbags.typesys.stores.ros2_humble import (
    geometry_msgs__msg__Quaternion as Quaternion,
)
from rosbags.typesys.stores.ros2_humble import geometry_msgs__msg__Vector3 as Vector3
from rosbags.typesys.stores.ros2_humble import sensor_msgs__msg__Imu as Imu
from rosbags.typesys.stores.ros2_humble import sensor_msgs__msg__NavSatFix as NavSatFix
from rosbags.typesys.stores.ros2_humble import (
    sensor_msgs__msg__NavSatStatus as NavSatStatus,
)
from rosbags.typesys.stores.ros2_humble import std_msgs__msg__Header as Header

logging.config.fileConfig("../logging.conf")
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(
        description="Comprehensive ROS2 bag processor: convert PX4 VehicleLocalPosition to IMU, "
        "SensorGps to NavSatFix, and pass through image data"
    )
    parser.add_argument(
        "-n",
        "--topic_names",
        nargs="+",
        default=[],
        help="Topic names to process (image, VehicleLocalPosition, and SensorGps topics)",
    )
    parser.add_argument(
        "-i", "--input_bag_file", default="", help="Path of input ROS2 bag file"
    )
    parser.add_argument(
        "-o",
        "--output_bag",
        default="./output_bag",
        help="Output ROS2 bag directory path",
    )
    parser.add_argument(
        "--start_offset",
        type=int,
        default=0,
        help="Number of messages to skip from start",
    )
    parser.add_argument(
        "--time_format",
        default="ros",
        choices=["ros", "unix"],
        help="Timestamp format: 'ros' for ROS timestamp or 'unix' for Unix timestamp",
    )

    return parser.parse_args()


def parse_px4_vehicle_local_position(rawdata):
    """
    Parse PX4 VehicleLocalPosition message from raw CDR data.
    Based on the message definition from px4_msgs/msg/VehicleLocalPosition.msg
    """
    # CDR format: skip 4-byte header
    offset = 4

    # Parse fields according to VehicleLocalPosition.msg structure:
    # uint64 timestamp
    timestamp = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint64 timestamp_sample
    timestamp_sample = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # bool fields (xy_valid, z_valid, v_xy_valid, v_z_valid)
    xy_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    z_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    v_xy_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    v_z_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1

    # CDR alignment: 4 bytes padding to align float32 values on 4-byte boundary
    offset += 0  # Already aligned

    # Position fields (float32 x, y, z)
    x = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    y = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    z = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Skip position reset delta fields (float32[2] delta_xy, uint8 xy_reset_counter, etc.)
    # float32[2] delta_xy
    offset += 8  # 2 * 4 bytes
    # uint8 xy_reset_counter
    offset += 1
    # float32 delta_z
    offset += 4
    # uint8 z_reset_counter
    offset += 1

    # CDR alignment: 2 bytes padding
    offset += 2

    # Velocity fields (float32 vx, vy, vz, z_deriv)
    vx = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    vy = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    vz = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    z_deriv = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Skip velocity reset delta fields
    # float32[2] delta_vxy
    offset += 8  # 2 * 4 bytes
    # uint8 vxy_reset_counter
    offset += 1
    # float32 delta_vz
    offset += 4
    # uint8 vz_reset_counter
    offset += 1

    # CDR alignment: 2 bytes padding
    offset += 2

    # Acceleration fields (float32 ax, ay, az)
    ax = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    ay = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    az = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Heading field (float32 heading) - this is what we need for IMU orientation
    heading = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    return {
        "timestamp": timestamp,
        "timestamp_sample": timestamp_sample,
        "xy_valid": xy_valid,
        "z_valid": z_valid,
        "v_xy_valid": v_xy_valid,
        "v_z_valid": v_z_valid,
        "x": x,
        "y": y,
        "z": z,
        "vx": vx,
        "vy": vy,
        "vz": vz,
        "z_deriv": z_deriv,
        "ax": ax,
        "ay": ay,
        "az": az,
        "heading": heading,
    }


def parse_px4_sensor_gps(rawdata):
    """
    Parse PX4 SensorGps message from raw CDR data.
    Based on the message definition from px4_msgs/msg/SensorGps.msg
    """
    # CDR format: skip 4-byte header
    offset = 4

    # Parse fields according to SensorGps.msg structure:
    # uint64 timestamp
    timestamp = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint64 timestamp_sample
    timestamp_sample = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint32 device_id
    device_id = struct.unpack("<I", rawdata[offset : offset + 4])[0]
    offset += 4

    # CDR alignment: 4 bytes padding to align double values on 8-byte boundary
    offset += 4

    # float64 latitude_deg
    latitude_deg = struct.unpack("<d", rawdata[offset : offset + 8])[0]
    offset += 8

    # float64 longitude_deg
    longitude_deg = struct.unpack("<d", rawdata[offset : offset + 8])[0]
    offset += 8

    # float64 altitude_msl_m
    altitude_msl_m = struct.unpack("<d", rawdata[offset : offset + 8])[0]
    offset += 8

    return {
        "timestamp": timestamp,
        "timestamp_sample": timestamp_sample,
        "device_id": device_id,
        "latitude_deg": latitude_deg,
        "longitude_deg": longitude_deg,
        "altitude_msl_m": altitude_msl_m,
    }


def heading_to_quaternion(heading):
    """
    Convert heading (yaw) to quaternion representation.
    Only the z-component (yaw) contains valid data.
    """
    # Convert heading to quaternion: (x=0, y=0, z=sin(heading/2), w=cos(heading/2))
    half_heading = heading / 2.0
    return [0.0, 0.0, math.sin(half_heading), math.cos(half_heading)]


def create_imu_message(vlp_data, timestamp_ros):
    """
    Create a sensor_msgs/Imu message from VehicleLocalPosition data.
    """
    # Convert heading to quaternion
    quat = heading_to_quaternion(vlp_data["heading"])

    # Create proper ROS2 message objects
    header = Header(
        stamp=Time(sec=timestamp_ros["sec"], nanosec=timestamp_ros["nanosec"]),
        frame_id="base_link",
    )

    orientation = Quaternion(x=quat[0], y=quat[1], z=quat[2], w=quat[3])

    angular_velocity = Vector3(x=0.0, y=0.0, z=0.0)

    linear_acceleration = Vector3(x=0.0, y=0.0, z=0.0)

    # Create IMU message with proper type
    imu_msg = Imu(
        header=header,
        orientation=orientation,
        # Set orientation covariance - only z (yaw) is valid, others are invalid (-1)
        orientation_covariance=np.array(
            [
                -1.0,
                0.0,
                0.0,  # x-axis (roll) - invalid
                0.0,
                -1.0,
                0.0,  # y-axis (pitch) - invalid
                0.0,
                0.0,
                0.1,  # z-axis (yaw) - valid with some variance
            ],
            dtype=np.float64,
        ),
        angular_velocity=angular_velocity,
        # Mark angular velocity as invalid
        angular_velocity_covariance=np.array(
            [
                -1.0,
                0.0,
                0.0,
                0.0,
                -1.0,
                0.0,
                0.0,
                0.0,
                -1.0,
            ],
            dtype=np.float64,
        ),
        linear_acceleration=linear_acceleration,
        # Mark linear acceleration as invalid
        linear_acceleration_covariance=np.array(
            [
                -1.0,
                0.0,
                0.0,
                0.0,
                -1.0,
                0.0,
                0.0,
                0.0,
                -1.0,
            ],
            dtype=np.float64,
        ),
    )

    return imu_msg


def create_gps_message(gps_data, timestamp_ros):
    """
    Create a sensor_msgs/NavSatFix message from SensorGps data.
    """
    # Create proper ROS2 message objects
    header = Header(
        stamp=Time(sec=timestamp_ros["sec"], nanosec=timestamp_ros["nanosec"]),
        frame_id="gps",
    )

    # Create GPS status - assume GPS fix is available if coordinates are valid
    status = NavSatStatus(
        status=NavSatStatus.STATUS_FIX,  # GPS fix available
        service=NavSatStatus.SERVICE_GPS,  # GPS service
    )

    # Check if GPS fix is valid (not NaN)
    latitude = gps_data["latitude_deg"]
    longitude = gps_data["longitude_deg"]
    altitude = gps_data["altitude_msl_m"]

    if (
        latitude != latitude or longitude != longitude or altitude != altitude
    ):  # NaN check
        status.status = NavSatStatus.STATUS_NO_FIX
        latitude = longitude = altitude = 0.0

    # Create NavSatFix message
    gps_msg = NavSatFix(
        header=header,
        status=status,
        latitude=latitude,
        longitude=longitude,
        altitude=altitude,
        position_covariance=np.zeros(9, dtype=np.float64),  # Unknown covariance
        position_covariance_type=NavSatFix.COVARIANCE_TYPE_UNKNOWN,
    )

    return gps_msg


def process_bag(args):
    """Process the input bag file and create output bag with converted messages."""
    # Remove output directory if it exists (Writer will create it)
    if os.path.exists(args.output_bag):
        import shutil

        shutil.rmtree(args.output_bag)

    typestore = get_typestore(Stores.ROS2_HUMBLE)
    processed_vlp_count = 0
    processed_gps_count = 0
    processed_image_count = 0
    skip_count = 0

    with Reader(args.input_bag_file) as reader:
        with Writer(args.output_bag, version=9) as writer:
            # Add connections for output topics
            imu_connection = writer.add_connection(
                "/imu/data", "sensor_msgs/msg/Imu", typestore=typestore
            )
            gps_connection = writer.add_connection(
                "/gps/fix", "sensor_msgs/msg/NavSatFix", typestore=typestore
            )

            # Dictionary to store image connections
            image_connections = {}

            for connection, timestamp, rawdata in reader.messages():
                if connection.topic in args.topic_names:
                    # Skip messages based on start_offset
                    if skip_count < args.start_offset:
                        skip_count += 1
                        continue

                    try:
                        if connection.msgtype == "px4_msgs/msg/VehicleLocalPosition":
                            # Parse VehicleLocalPosition message
                            vlp_data = parse_px4_vehicle_local_position(rawdata)

                            # Convert timestamp
                            if args.time_format == "ros":
                                timestamp_sec = vlp_data["timestamp"] // 1000000
                                timestamp_nsec = (
                                    vlp_data["timestamp"] % 1000000
                                ) * 1000
                                timestamp_ros = {
                                    "sec": int(timestamp_sec),
                                    "nanosec": int(timestamp_nsec),
                                }
                            else:  # unix
                                timestamp_unix = vlp_data["timestamp"] * 1e-6
                                timestamp_sec = int(timestamp_unix)
                                timestamp_nsec = int(
                                    (timestamp_unix - timestamp_sec) * 1e9
                                )
                                timestamp_ros = {
                                    "sec": timestamp_sec,
                                    "nanosec": timestamp_nsec,
                                }

                            # Create IMU message
                            imu_msg = create_imu_message(vlp_data, timestamp_ros)

                            # Serialize and write IMU message
                            imu_data = typestore.serialize_cdr(
                                imu_msg, imu_connection.msgtype
                            )
                            writer.write(imu_connection, timestamp, imu_data)
                            processed_vlp_count += 1

                            if processed_vlp_count % 1000 == 0:
                                logger.info(
                                    f"Converted {processed_vlp_count} VehicleLocalPosition messages to IMU"
                                )

                        elif connection.msgtype == "px4_msgs/msg/SensorGps":
                            # Parse SensorGps message
                            gps_data = parse_px4_sensor_gps(rawdata)

                            # Convert timestamp
                            if args.time_format == "ros":
                                timestamp_sec = gps_data["timestamp"] // 1000000
                                timestamp_nsec = (
                                    gps_data["timestamp"] % 1000000
                                ) * 1000
                                timestamp_ros = {
                                    "sec": int(timestamp_sec),
                                    "nanosec": int(timestamp_nsec),
                                }
                            else:  # unix
                                timestamp_unix = gps_data["timestamp"] * 1e-6
                                timestamp_sec = int(timestamp_unix)
                                timestamp_nsec = int(
                                    (timestamp_unix - timestamp_sec) * 1e9
                                )
                                timestamp_ros = {
                                    "sec": timestamp_sec,
                                    "nanosec": timestamp_nsec,
                                }

                            # Create GPS message
                            gps_msg = create_gps_message(gps_data, timestamp_ros)

                            # Serialize and write GPS message
                            gps_data_serialized = typestore.serialize_cdr(
                                gps_msg, gps_connection.msgtype
                            )
                            writer.write(gps_connection, timestamp, gps_data_serialized)
                            processed_gps_count += 1

                            if processed_gps_count % 1000 == 0:
                                logger.info(
                                    f"Converted {processed_gps_count} SensorGps messages to NavSatFix"
                                )

                        elif connection.msgtype in [
                            "sensor_msgs/msg/Image",
                            "sensor_msgs/msg/CompressedImage",
                        ]:
                            # Handle image messages - pass through unchanged
                            if connection.topic not in image_connections:
                                image_connections[connection.topic] = (
                                    writer.add_connection(
                                        connection.topic,
                                        connection.msgtype,
                                        typestore=typestore,
                                    )
                                )

                            writer.write(
                                image_connections[connection.topic], timestamp, rawdata
                            )
                            processed_image_count += 1

                            if processed_image_count % 1000 == 0:
                                logger.info(
                                    f"Processed {processed_image_count} image messages"
                                )

                        else:
                            logger.warning(
                                f"Unsupported message type: {connection.msgtype}"
                            )

                    except Exception as e:
                        logger.warning(f"Failed to process message: {e}")
                        continue

    logger.info("Processing complete:")
    logger.info(
        f"  - Converted {processed_vlp_count} VehicleLocalPosition messages to IMU"
    )
    logger.info(f"  - Converted {processed_gps_count} SensorGps messages to NavSatFix")
    logger.info(f"  - Passed through {processed_image_count} image messages")
    logger.info(f"  - Output bag saved to: {args.output_bag}")

    return processed_vlp_count, processed_gps_count, processed_image_count


def main():
    args = parser()

    if not args.topic_names:
        logger.error("Please specify at least one topic name using -n argument")
        return

    if not os.path.exists(args.input_bag_file):
        logger.error(f"Input bag file does not exist: {args.input_bag_file}")
        return

    logger.info(f"Processing topics: {args.topic_names}")
    logger.info(f"Input bag file: {args.input_bag_file}")
    logger.info(f"Output bag: {args.output_bag}")

    # Process the bag file
    processed_vlp_count, processed_gps_count, processed_image_count = process_bag(args)

    if processed_vlp_count > 0 or processed_gps_count > 0 or processed_image_count > 0:
        logger.info("Successfully processed bag file")
        logger.info(
            f"Total VehicleLocalPosition messages converted: {processed_vlp_count}"
        )
        logger.info(f"Total SensorGps messages converted: {processed_gps_count}")
        logger.info(f"Total image messages passed through: {processed_image_count}")
    else:
        logger.warning("No data found in the specified topics")


if __name__ == "__main__":
    main()
