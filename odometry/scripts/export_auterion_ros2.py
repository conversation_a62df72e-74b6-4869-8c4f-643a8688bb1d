#!/usr/bin/env python3
"""
ROS2 Bag Processing Tool: VehicleLocalPosition to IMU Converter

This script processes ROS2 bag files containing both image data and PX4 VehicleLocalPosition
messages, converting VehicleLocalPosition to standard ROS2 sensor_msgs/Imu messages while
preserving image data unchanged.

Key Features:
- Processes both image topics and VehicleLocalPosition topics from ROS2 bag files
- Converts PX4 VehicleLocalPosition messages to standard sensor_msgs/Imu messages
- Passes through image data completely unchanged (no modifications)
- Outputs a new ROS2 bag file with processed data
- Uses rosbags library for consistent ROS2 bag processing
- Preserves original timestamps for both image and IMU messages

Data Conversion Details:
- VehicleLocalPosition heading → IMU quaternion: (x=0, y=0, z=sin(heading/2), w=cos(heading/2))
- Linear acceleration: Set to (0, 0, 0) with invalid covariance (-1)
- Angular velocity: Set to (0, 0, 0) with invalid covariance (-1)
- Only orientation z-component (yaw) contains valid data
- Orientation covariance: invalid for x,y (-1), valid for z (0.1)

Supported Message Types:
- Input: px4_msgs/msg/VehicleLocalPosition → Output: sensor_msgs/msg/Imu
- Input: sensor_msgs/msg/Image → Output: sensor_msgs/msg/Image (unchanged)
- Input: sensor_msgs/msg/CompressedImage → Output: sensor_msgs/msg/CompressedImage (unchanged)

Usage Examples:
    # Process both image and VehicleLocalPosition topics
    python export_vehicle_local_position_to_imu.py \
        -i /path/to/input/bag \
        -n /camera/image_raw /fmu/out/vehicle_local_position \
        -o /path/to/output/bag

    # With custom start offset and ROS timestamp format
    python export_vehicle_local_position_to_imu.py \
        -i /media/aabouee/data/bags/Auterion/22.01.25.14-07-IR-9min-140 \
        -n /camera/image_raw /fmu/out/vehicle_local_position \
        -o ./output/processed_bag \
        --start_offset 100 \
        --time_format ros

Output ROS2 Bag Structure:
    - Original image topics: /camera/image_raw (sensor_msgs/msg/Image)
    - Converted IMU topic: /imu/data (sensor_msgs/msg/Imu)

Dependencies:
    - rosbags (for ROS2 bag processing)
    - Standard Python libraries: argparse, logging, math, os, struct

Author: Generated for spl-gnss-denied-evaluation project
Compatible with: PX4 VehicleLocalPosition messages, ROS2 bag files, image topics
"""

import argparse
import logging.config
import math
import os
import struct

from rosbags.rosbag2 import Reader, Writer
from rosbags.typesys import Stores, get_typestore

logging.config.fileConfig("../logging.conf")
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(
        description="Process ROS2 bag files: convert VehicleLocalPosition to IMU and pass through image data"
    )
    parser.add_argument(
        "-n",
        "--topic_names",
        nargs="+",
        default=[],
        help="Topic names to process (both image topics and VehicleLocalPosition topics)",
    )
    parser.add_argument(
        "-i", "--input_bag_file", default="", help="Path of input ROS2 bag file"
    )
    parser.add_argument(
        "-o",
        "--output_bag",
        default="./output_bag",
        help="Output ROS2 bag directory path",
    )
    parser.add_argument(
        "--start_offset",
        type=int,
        default=0,
        help="Number of messages to skip from start",
    )
    parser.add_argument(
        "--time_format",
        default="ros",
        choices=["ros", "unix"],
        help="Timestamp format: 'ros' for ROS timestamp or 'unix' for Unix timestamp",
    )

    return parser.parse_args()


def parse_px4_vehicle_local_position(rawdata):
    """
    Parse PX4 VehicleLocalPosition message from raw CDR data.
    Based on the message definition from px4_msgs/msg/VehicleLocalPosition.msg
    """
    # CDR format: skip 4-byte header
    offset = 4

    # Parse fields according to VehicleLocalPosition.msg structure:
    # uint64 timestamp
    timestamp = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint64 timestamp_sample
    timestamp_sample = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # bool fields (xy_valid, z_valid, v_xy_valid, v_z_valid)
    xy_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    z_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    v_xy_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1
    v_z_valid = struct.unpack("<?", rawdata[offset : offset + 1])[0]
    offset += 1

    # CDR alignment: 4 bytes padding to align float32 values on 4-byte boundary
    offset += 0  # Already aligned

    # Position fields (float32 x, y, z)
    x = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    y = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    z = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Skip position reset delta fields (float32[2] delta_xy, uint8 xy_reset_counter, etc.)
    # float32[2] delta_xy
    offset += 8  # 2 * 4 bytes
    # uint8 xy_reset_counter
    offset += 1
    # float32 delta_z
    offset += 4
    # uint8 z_reset_counter
    offset += 1

    # CDR alignment: 2 bytes padding
    offset += 2

    # Velocity fields (float32 vx, vy, vz, z_deriv)
    vx = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    vy = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    vz = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    z_deriv = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Skip velocity reset delta fields
    # float32[2] delta_vxy
    offset += 8  # 2 * 4 bytes
    # uint8 vxy_reset_counter
    offset += 1
    # float32 delta_vz
    offset += 4
    # uint8 vz_reset_counter
    offset += 1

    # CDR alignment: 2 bytes padding
    offset += 2

    # Acceleration fields (float32 ax, ay, az)
    ax = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    ay = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4
    az = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    # Heading field (float32 heading) - this is what we need for IMU orientation
    heading = struct.unpack("<f", rawdata[offset : offset + 4])[0]
    offset += 4

    return {
        "timestamp": timestamp,
        "timestamp_sample": timestamp_sample,
        "xy_valid": xy_valid,
        "z_valid": z_valid,
        "v_xy_valid": v_xy_valid,
        "v_z_valid": v_z_valid,
        "x": x,
        "y": y,
        "z": z,
        "vx": vx,
        "vy": vy,
        "vz": vz,
        "z_deriv": z_deriv,
        "ax": ax,
        "ay": ay,
        "az": az,
        "heading": heading,
    }


def heading_to_quaternion(heading):
    """
    Convert heading (yaw) to quaternion representation.
    Only the z-component (yaw) contains valid data.
    """
    # Convert heading to quaternion: (x=0, y=0, z=sin(heading/2), w=cos(heading/2))
    half_heading = heading / 2.0
    return [0.0, 0.0, math.sin(half_heading), math.cos(half_heading)]


def create_imu_message(vlp_data, timestamp_ros):
    """
    Create a sensor_msgs/Imu message from VehicleLocalPosition data.
    """
    # Convert heading to quaternion
    quat = heading_to_quaternion(vlp_data["heading"])

    # Create IMU message structure compatible with sensor_msgs/msg/Imu
    imu_msg = {
        "header": {
            "stamp": timestamp_ros,
            "frame_id": "base_link",  # Standard frame for IMU data
        },
        "orientation": {
            "x": quat[0],
            "y": quat[1],
            "z": quat[2],
            "w": quat[3],
        },
        # Set orientation covariance - only z (yaw) is valid, others are invalid (-1)
        "orientation_covariance": [
            -1.0,
            0.0,
            0.0,  # x-axis (roll) - invalid
            0.0,
            -1.0,
            0.0,  # y-axis (pitch) - invalid
            0.0,
            0.0,
            0.1,  # z-axis (yaw) - valid with some variance
        ],
        "angular_velocity": {
            "x": 0.0,  # Set to zero as requested
            "y": 0.0,  # Set to zero as requested
            "z": 0.0,  # Set to zero as requested
        },
        # Mark angular velocity as invalid
        "angular_velocity_covariance": [
            -1.0,
            0.0,
            0.0,
            0.0,
            -1.0,
            0.0,
            0.0,
            0.0,
            -1.0,
        ],
        "linear_acceleration": {
            "x": 0.0,  # Set to zero as requested
            "y": 0.0,  # Set to zero as requested
            "z": 0.0,  # Set to zero as requested
        },
        # Mark linear acceleration as invalid
        "linear_acceleration_covariance": [
            -1.0,
            0.0,
            0.0,
            0.0,
            -1.0,
            0.0,
            0.0,
            0.0,
            -1.0,
        ],
    }

    return imu_msg


def process_bag(args):
    """Process the input bag file and create output bag with converted messages."""
    # Remove output directory if it exists (Writer will create it)
    if os.path.exists(args.output_bag):
        import shutil

        shutil.rmtree(args.output_bag)

    typestore = get_typestore(Stores.ROS2_HUMBLE)

    processed_vlp_count = 0
    processed_image_count = 0
    skip_count = 0

    with Reader(args.input_bag_file) as reader:
        with Writer(args.output_bag) as writer:
            # Add connection for IMU output topic
            imu_connection = writer.add_connection(
                "/imu/data", "sensor_msgs/msg/Imu", typestore=typestore
            )

            # Dictionary to store image connections
            image_connections = {}

            for connection, timestamp, rawdata in reader.messages():
                if connection.topic in args.topic_names:
                    # Skip messages based on start_offset
                    if skip_count < args.start_offset:
                        skip_count += 1
                        continue

                    try:
                        if connection.msgtype == "px4_msgs/msg/VehicleLocalPosition":
                            # Parse VehicleLocalPosition message
                            vlp_data = parse_px4_vehicle_local_position(rawdata)

                            # Convert timestamp
                            if args.time_format == "ros":
                                timestamp_sec = vlp_data["timestamp"] // 1000000
                                timestamp_nsec = (
                                    vlp_data["timestamp"] % 1000000
                                ) * 1000
                                timestamp_ros = {
                                    "sec": int(timestamp_sec),
                                    "nanosec": int(timestamp_nsec),
                                }
                            else:  # unix
                                timestamp_unix = vlp_data["timestamp"] * 1e-6
                                timestamp_sec = int(timestamp_unix)
                                timestamp_nsec = int(
                                    (timestamp_unix - timestamp_sec) * 1e9
                                )
                                timestamp_ros = {
                                    "sec": timestamp_sec,
                                    "nanosec": timestamp_nsec,
                                }

                            # Create IMU message
                            imu_msg = create_imu_message(vlp_data, timestamp_ros)

                            # Serialize and write IMU message
                            imu_data = typestore.serialize_cdr(
                                imu_msg, "sensor_msgs/msg/Imu"
                            )
                            writer.write(imu_connection, timestamp, imu_data)
                            processed_vlp_count += 1

                            if processed_vlp_count % 1000 == 0:
                                logger.info(
                                    f"Converted {processed_vlp_count} VehicleLocalPosition messages to IMU"
                                )

                        elif connection.msgtype in [
                            "sensor_msgs/msg/Image",
                            "sensor_msgs/msg/CompressedImage",
                        ]:
                            # Handle image messages - pass through unchanged
                            if connection.topic not in image_connections:
                                image_connections[connection.topic] = (
                                    writer.add_connection(
                                        connection.topic,
                                        connection.msgtype,
                                        typestore=typestore,
                                    )
                                )

                            writer.write(
                                image_connections[connection.topic], timestamp, rawdata
                            )
                            processed_image_count += 1

                            if processed_image_count % 1000 == 0:
                                logger.info(
                                    f"Processed {processed_image_count} image messages"
                                )

                        else:
                            logger.warning(
                                f"Unsupported message type: {connection.msgtype}"
                            )

                    except Exception as e:
                        logger.warning(f"Failed to process message: {e}")
                        continue

    logger.info(f"Processing complete:")
    logger.info(
        f"  - Converted {processed_vlp_count} VehicleLocalPosition messages to IMU"
    )
    logger.info(f"  - Passed through {processed_image_count} image messages")
    logger.info(f"  - Output bag saved to: {args.output_bag}")

    return processed_vlp_count, processed_image_count


def main():
    args = parser()

    if not args.topic_names:
        logger.error("Please specify at least one topic name using -n argument")
        return

    if not os.path.exists(args.input_bag_file):
        logger.error(f"Input bag file does not exist: {args.input_bag_file}")
        return

    logger.info(f"Processing topics: {args.topic_names}")
    logger.info(f"Input bag file: {args.input_bag_file}")
    logger.info(f"Output bag: {args.output_bag}")

    # Process the bag file
    processed_vlp_count, processed_image_count = process_bag(args)

    if processed_vlp_count > 0 or processed_image_count > 0:
        logger.info("Successfully processed bag file")
        logger.info(
            f"Total VehicleLocalPosition messages converted: {processed_vlp_count}"
        )
        logger.info(f"Total image messages passed through: {processed_image_count}")
    else:
        logger.warning("No data found in the specified topics")


if __name__ == "__main__":
    main()
