import argparse
import csv
import logging.config
import os
import struct

from rosbags.rosbag2 import Reader
from rosbags.typesys import Stores, get_typestore

logging.config.fileConfig("../logging.conf")
logger = logging.getLogger(__name__)


def parser():
    parser = argparse.ArgumentParser(
        description="Extract PX4 SensorGps topic from a ros2 bag file and store data in CSV format"
    )
    parser.add_argument(
        "-n",
        "--topic_names",
        nargs="+",
        default=[],
        help="PX4 SensorGps topic names to extract",
    )
    parser.add_argument(
        "-i", "--input_bag_file", default="", help="Path of ros2 bag file"
    )
    parser.add_argument(
        "-o", "--output_file", default="./gps_px4_data.csv", help="Output CSV file path"
    )
    parser.add_argument(
        "--start_offset",
        type=int,
        default=0,
        help="Number of GPS messages to skip from start",
    )
    parser.add_argument(
        "--time_format",
        default="ros",
        choices=["ros", "unix"],
        help="Timestamp format: 'ros' for ROS timestamp or 'unix' for Unix timestamp",
    )

    return parser.parse_args()


def parse_px4_sensor_gps(rawdata):
    """
    Parse PX4 SensorGps message from raw CDR data.
    Based on the complete message definition from px4_msgs/msg/SensorGps.msg
    Note: CDR alignment requires 8-byte alignment for double values
    """
    # CDR format: skip 4-byte header
    offset = 4

    # Parse fields according to SensorGps.msg structure:
    # uint64 timestamp
    timestamp = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint64 timestamp_sample
    timestamp_sample = struct.unpack("<Q", rawdata[offset : offset + 8])[0]
    offset += 8

    # uint32 device_id
    device_id = struct.unpack("<I", rawdata[offset : offset + 4])[0]
    offset += 4

    # CDR alignment: 4 bytes padding to align double values on 8-byte boundary
    offset += 4

    # float64 latitude_deg
    latitude_deg = struct.unpack("<d", rawdata[offset : offset + 8])[0]
    offset += 8

    # float64 longitude_deg
    longitude_deg = struct.unpack("<d", rawdata[offset : offset + 8])[0]
    offset += 8

    # float64 altitude_msl_m
    altitude_msl_m = struct.unpack("<d", rawdata[offset : offset + 8])[0]
    offset += 8

    return {
        "timestamp": timestamp,
        "timestamp_sample": timestamp_sample,
        "device_id": device_id,
        "latitude_deg": latitude_deg,
        "longitude_deg": longitude_deg,
        "altitude_msl_m": altitude_msl_m,
    }


def extract_gps_data(args):
    """Extract GPS data from a ros2 bag file and save to CSV."""
    gps_data = []
    skip_count = 0

    with Reader(args.input_bag_file) as reader:
        for connection, timestamp, rawdata in reader.messages():
            if connection.topic in args.topic_names:
                # Skip messages based on start_offset
                if skip_count < args.start_offset:
                    skip_count += 1
                    continue

                # Handle PX4 SensorGps message type
                if connection.msgtype == "px4_msgs/msg/SensorGps":
                    try:
                        # Parse the raw CDR data manually
                        msg_data = parse_px4_sensor_gps(rawdata)

                        # Extract timestamp
                        if args.time_format == "ros":
                            # Convert microseconds to seconds with nanosecond precision format
                            timestamp_sec = msg_data["timestamp"] // 1000000
                            timestamp_nsec = (msg_data["timestamp"] % 1000000) * 1000
                            timestamp_val = f"{timestamp_sec}.{timestamp_nsec:09d}"
                        else:  # unix
                            # Convert microseconds to seconds
                            timestamp_val = msg_data["timestamp"] * 1e-6

                        # Extract GPS coordinates
                        latitude = msg_data["latitude_deg"]
                        longitude = msg_data["longitude_deg"]
                        altitude = msg_data["altitude_msl_m"]

                        # Check if GPS fix is valid (not NaN)
                        if not (
                            latitude != latitude
                            or longitude != longitude
                            or altitude != altitude
                        ):  # NaN check
                            gps_data.append(
                                [timestamp_val, latitude, longitude, altitude]
                            )
                    except Exception as e:
                        logger.warning(f"Failed to parse SensorGps message: {e}")
                        continue

                else:
                    logger.warning(
                        f"Unsupported GPS message type: {connection.msgtype}"
                    )
                    continue

    return gps_data


def save_to_csv(gps_data, output_file):
    """Save GPS data to CSV file."""
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        # Write header
        writer.writerow(["timestamp", "latitude", "longitude", "altitude"])
        # Write data
        writer.writerows(gps_data)

    print(f"GPS data saved to {output_file} with {len(gps_data)} records")


def main():
    args = parser()

    if not args.topic_names:
        logger.error(
            "Please specify at least one PX4 SensorGps topic name using -n argument"
        )
        return

    if not os.path.exists(args.input_bag_file):
        logger.error(f"Input bag file does not exist: {args.input_bag_file}")
        return

    logger.info(f"Extracting PX4 SensorGps data from topics: {args.topic_names}")
    logger.info(f"Input bag file: {args.input_bag_file}")
    logger.info(f"Output CSV file: {args.output_file}")

    # Extract GPS data
    gps_data = extract_gps_data(args)

    if gps_data:
        # Save to CSV
        save_to_csv(gps_data, args.output_file)
        logger.info(f"Successfully extracted {len(gps_data)} GPS records")
    else:
        logger.warning("No GPS data found in the specified topics")


if __name__ == "__main__":
    main()
